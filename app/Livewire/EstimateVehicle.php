<?php

namespace App\Livewire;

use App\Models\Vehicle\VehicleMake;
use App\Models\Vehicle\VehicleModel;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Livewire\Component;

class EstimateVehicle extends Component implements HasForms
{
    use InteractsWithForms;

    public ?array $data = [];

    public function mount(): void
    {
        $this->form->fill();
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('marca')
                    ->label('Marca')
                    ->options(VehicleMake::pluck('name', 'id'))
                    ->searchable()
                    ->preload()
                    ->required()
                    ->live()
                    ->placeholder('Selecciona una Marca'),

                Select::make('modelo')
                    ->label('Modelo')
                    ->options(function (Get $get) {
                        $makeId = $get('marca');
                        if (! $makeId) {
                            return [];
                        }

                        return VehicleModel::where('vehicle_make_id', $makeId)
                            ->pluck('name', 'id');
                    })
                    ->searchable()
                    ->required()
                    ->placeholder('Selecciona un modelo')
                    ->disabled(fn (Get $get) => ! $get('marca')),

                TextInput::make('ano')
                    ->label('Año')
                    ->numeric()
                    ->required()
                    ->minValue(1900)
                    ->maxValue(date('Y') + 1),

                TextInput::make('suma')
                    ->label('Suma Asegurada')
                    ->numeric()
                    ->required()
                    ->prefix('$'),

                Select::make('plan')
                    ->label('Plan')
                    ->options([
                        'Mensual full' => 'Mensual Full',
                        'Anual full' => 'Anual Full',
                    ])
                    ->default('Mensual full')
                    ->required(),

                Select::make('uso')
                    ->label('Uso')
                    ->options([
                        'Privado' => 'Privado',
                        'Publico' => 'Público',
                    ])
                    ->default('Privado')
                    ->required(),

                Select::make('estado')
                    ->label('Estado')
                    ->options([
                        'Nuevo' => 'Nuevo',
                        'Usado' => 'Usado',
                    ])
                    ->default('Nuevo')
                    ->required(),

                Checkbox::make('salvamento')
                    ->label('Salvamento')
                    ->inline(false),
            ])
            ->statePath('data')
            ->columns();
    }

    public function create(): void
    {
        $data = $this->form->getState();

        // Here you can process the form data
        // For example, send to cotizaciones endpoint or save to database

        // For now, just dump the data to see the structure
        dd($data);
    }

    public function submit(): void
    {
        $this->create();
    }

    public function render()
    {
        return view('livewire.estimate-vehicle');
    }
}
