<?php

namespace App\Models\Vehicle;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class VehicleModel extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'id','name', 'vehicle_make_id', 'vehicle_type_id',
    ];

    public function make(): BelongsTo
    {
        return $this->belongsTo(VehicleMake::class,'vehicle_make_id');
    }

    public function type(): BelongsTo
    {
        return $this->belongsTo(VehicleType::class,'vehicle_type_id');
    }
}
