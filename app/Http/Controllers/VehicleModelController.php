<?php

namespace App\Http\Controllers;

use App\Http\Requests\VehicleModel\StoreVehicleModelRequest;
use App\Http\Requests\VehicleModel\UpdateVehicleModelRequest;
use App\Http\Resources\VehicleModelResource;
use App\Repositories\VehicleModelRepository;
use Illuminate\Http\Request;

class VehicleModelController extends Controller
{
    public function __construct(protected VehicleModelRepository $repository)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $list = $this->repository->search();

        return VehicleModelResource::collection($list);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreVehicleModelRequest $request)
    {
        $model = $this->repository->store($request->validated());

        return new VehicleModelResource($model);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $model = $this->repository->getById($id);

        return new VehicleModelResource($model);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateVehicleModelRequest $request, string $id)
    {
        $model = $this->repository->update($id, $request->validated());

        return new VehicleModelResource($model);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->repository->destroy($id);

        return response()->noContent();
    }

    public function restore(string $id)
    {
        $model = $this->repository->restore($id);

        return new VehicleModelResource($model);
    }
}
