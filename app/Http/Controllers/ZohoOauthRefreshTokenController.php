<?php

namespace App\Http\Controllers;

use App\Http\Requests\ZohoOauthRefreshToken\StoreZohoOauthRefreshTokenRequest;
use App\Http\Requests\ZohoOauthRefreshToken\UpdateZohoOauthRefreshTokenRequest;
use App\Http\Resources\ZohoOauthRefreshTokenResource;
use App\Repositories\ZohoOauthRefreshTokenRepository;
use Illuminate\Http\Request;

class ZohoOauthRefreshTokenController extends Controller
{
    public function __construct(protected ZohoOauthRefreshTokenRepository $repository)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $list = $this->repository->search();

        return ZohoOauthRefreshTokenResource::collection($list);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreZohoOauthRefreshTokenRequest $request)
    {
        $model = $this->repository->store($request->validated());

        return new ZohoOauthRefreshTokenResource($model);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $model = $this->repository->getById($id);

        return new ZohoOauthRefreshTokenResource($model);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateZohoOauthRefreshTokenRequest $request, string $id)
    {
        $model = $this->repository->update($id, $request->validated());

        return new ZohoOauthRefreshTokenResource($model);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->repository->destroy($id);

        return response()->noContent();
    }

    public function restore(string $id)
    {
        $model = $this->repository->restore($id);

        return new ZohoOauthRefreshTokenResource($model);
    }
}
