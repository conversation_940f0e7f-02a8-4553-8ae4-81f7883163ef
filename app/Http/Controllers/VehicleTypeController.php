<?php

namespace App\Http\Controllers;

use App\Http\Requests\VehicleType\StoreVehicleTypeRequest;
use App\Http\Requests\VehicleType\UpdateVehicleTypeRequest;
use App\Http\Resources\VehicleTypeResource;
use App\Repositories\VehicleTypeRepository;

class VehicleTypeController extends Controller
{
    public function __construct(protected VehicleTypeRepository $repository)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $list = $this->repository->search();

        return VehicleTypeResource::collection($list);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreVehicleTypeRequest $request)
    {
        $model = $this->repository->store($request->validated());

        return new VehicleTypeResource($model);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $model = $this->repository->getById($id);

        return new VehicleTypeResource($model);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateVehicleTypeRequest $request, string $id)
    {
        $model = $this->repository->update($id, $request->validated());

        return new VehicleTypeResource($model);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->repository->destroy($id);

        return response()->noContent();
    }

    public function restore(string $id)
    {
        $model = $this->repository->restore($id);

        return new VehicleTypeResource($model);
    }
}
