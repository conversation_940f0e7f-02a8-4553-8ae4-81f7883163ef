<?php

namespace App\Http\Controllers;

use App\Helpers\Cotizaciones;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Handle the incoming request.
     */
    public function __invoke(Request $request)
    {
        $libreria = new  Cotizaciones();
        $emisiones = $libreria->lista_emisiones();

        $lista = array();
        $polizas = 0;

        foreach ((array)$emisiones as $emision) {
            //filtrar por  mes y año actual
//            if (date("Y-m", strtotime($emision->getFieldValue("Vigencia_desde"))) == date("Y-m")) {
                $lista[] = $emision->getFieldValue('Coberturas')->getLookupLabel();
                $polizas++;
//            }
        }

        return view('dashboard',[
            "titulo" => "Panel de Control",
            "lista" => array_count_values($lista),
            "polizas" => $polizas,
            "cotizaciones" => $emisiones,
        ]);
    }
}
