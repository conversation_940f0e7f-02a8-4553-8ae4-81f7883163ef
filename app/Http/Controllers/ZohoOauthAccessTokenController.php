<?php

namespace App\Http\Controllers;

use App\Http\Requests\ZohoOauthAccessToken\StoreZohoOauthAccessTokenRequest;
use App\Http\Requests\ZohoOauthAccessToken\UpdateZohoOauthAccessTokenRequest;
use App\Http\Resources\ZohoOauthAccessTokenResource;
use App\Repositories\ZohoOauthAccessTokenRepository;
use Illuminate\Http\Request;

class ZohoOauthAccessTokenController extends Controller
{
    public function __construct(protected ZohoOauthAccessTokenRepository $repository)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $list = $this->repository->search();

        return ZohoOauthAccessTokenResource::collection($list);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreZohoOauthAccessTokenRequest $request)
    {
        $model = $this->repository->store($request->validated());

        return new ZohoOauthAccessTokenResource($model);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $model = $this->repository->getById($id);

        return new ZohoOauthAccessTokenResource($model);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateZohoOauthAccessTokenRequest $request, string $id)
    {
        $model = $this->repository->update($id, $request->validated());

        return new ZohoOauthAccessTokenResource($model);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->repository->destroy($id);

        return response()->noContent();
    }

    public function restore(string $id)
    {
        $model = $this->repository->restore($id);

        return new ZohoOauthAccessTokenResource($model);
    }
}
