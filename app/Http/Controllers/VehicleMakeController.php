<?php

namespace App\Http\Controllers;

use App\Http\Requests\VehicleMake\StoreVehicleMakeRequest;
use App\Http\Requests\VehicleMake\UpdateVehicleMakeRequest;
use App\Http\Resources\VehicleMakeResource;
use App\Repositories\VehicleMakeRepository;
use Illuminate\Http\Request;

class VehicleMakeController extends Controller
{
    public function __construct(protected VehicleMakeRepository $repository)
    {
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $list = $this->repository->search();

        return VehicleMakeResource::collection($list);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreVehicleMakeRequest $request)
    {
        $model = $this->repository->store($request->validated());

        return new VehicleMakeResource($model);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $model = $this->repository->getById($id);

        return new VehicleMakeResource($model);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateVehicleMakeRequest $request, string $id)
    {
        $model = $this->repository->update($id, $request->validated());

        return new VehicleMakeResource($model);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $this->repository->destroy($id);

        return response()->noContent();
    }

    public function restore(string $id)
    {
        $model = $this->repository->restore($id);

        return new VehicleMakeResource($model);
    }
}
