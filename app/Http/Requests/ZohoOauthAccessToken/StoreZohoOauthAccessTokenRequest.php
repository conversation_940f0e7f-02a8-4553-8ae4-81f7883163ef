<?php

namespace App\Http\Requests\ZohoOauthAccessToken;

use Illuminate\Foundation\Http\FormRequest;

class StoreZohoOauthAccessTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'access_token' => ['required', 'string'],
            'expires_at' => ['required', 'date'],
            'revoked' => ['boolean'],
            'scopes' => ['nullable', 'string'],
        ];
    }
}
