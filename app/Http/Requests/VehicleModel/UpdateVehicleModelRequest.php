<?php

namespace App\Http\Requests\VehicleModel;

use Illuminate\Foundation\Http\FormRequest;

class UpdateVehicleModelRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'vehicle_make_id' => ['required', 'exists:vehicle_makes,id'],
            'vehicle_type_id' => ['required', 'exists:vehicle_types,id'],
        ];
    }
}
